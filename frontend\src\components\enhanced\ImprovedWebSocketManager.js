import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { message } from 'antd';
import {
  SendOutlined,
  ReloadOutlined,
  DisconnectOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CodeOutlined
} from '@ant-design/icons';

import { styled } from '../../design-system';
import theme from '../../design-system/theme';

// Import custom hooks
import useEnhancedWebSocket from '../../hooks/useEnhancedWebSocket';

// Import accessible components
import AccessibleCard from '../a11y/AccessibleCard';
import AccessibleButton from '../a11y/AccessibleButton';
import AccessibleInput from '../a11y/AccessibleInput';

const WebSocketManagerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[4]};
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  padding: ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.connected ? theme.colors.success.light : theme.colors.error.light};
  color: ${props => props.connected ? theme.colors.success.dark : theme.colors.error.dark};
`;

const MessageList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
  max-height: 400px;
  overflow-y: auto;
  padding: ${theme.spacing[2]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.neutral[50]};
`;

const Message = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.type === 'sent' ? theme.colors.primary.light : 'white'};
  border-left: 4px solid ${props => props.type === 'sent' ? theme.colors.primary.main : theme.colors.secondary.main};
  box-shadow: ${theme.shadows.sm};

  .message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[1]};
    font-size: ${theme.typography.fontSize.sm};
    color: ${theme.colors.neutral[500]};
  }

  .timestamp {
    font-family: monospace;
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.neutral[600]};
    background-color: ${theme.colors.neutral[100]};
    padding: 2px 4px;
    border-radius: 3px;
  }

  .message-content {
    word-break: break-word;
  }

  pre {
    background-color: ${theme.colors.neutral[100]};
    padding: ${theme.spacing[2]};
    border-radius: ${theme.borderRadius.sm};
    overflow-x: auto;
    font-family: ${theme.typography.fontFamily.code};
    font-size: ${theme.typography.fontSize.sm};
    margin: ${theme.spacing[2]} 0 0 0;
  }
`;

const MessageInput = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
`;

const SettingsPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[3]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
`;

const SettingsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${theme.colors.neutral[200]};
  margin-bottom: ${theme.spacing[4]};
`;

const Tab = styled.button`
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  cursor: pointer;
  border: none;
  background: none;
  border-bottom: 2px solid ${props => props.active ? theme.colors.primary.main : 'transparent'};
  color: ${props => props.active ? theme.colors.primary.main : theme.colors.neutral[700]};
  font-weight: ${props => props.active ? theme.typography.fontWeight.semibold : theme.typography.fontWeight.medium};

  &:hover {
    color: ${theme.colors.primary.main};
  }

  &:focus {
    outline: 2px solid ${theme.colors.primary.main};
    outline-offset: 2px;
  }
`;

const MessageTemplates = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${theme?.spacing?.[2] || '8px'};
  margin-bottom: ${theme?.spacing?.[3] || '12px'};
`;

/**
 * Improved WebSocket Manager with better accessibility and code reuse
 */
const ImprovedWebSocketManager = () => {
  const { url: storeUrl } = useSelector(state => ({
    url: state?.websocket?.url || null
  }));

  const [activeTab, setActiveTab] = useState('messages');
  const [wsUrl, setWsUrl] = useState(storeUrl || 'ws://localhost:8000/ws/');
  const [messageText, setMessageText] = useState('');
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [showTimestamp, setShowTimestamp] = useState(true);

  const messagesEndRef = useRef(null);

  // Use the enhanced WebSocket hook with optimized settings
  const {
    connected,
    connecting,
    reconnectAttempt,
    messages,
    error,
    connect,
    disconnect,
    sendMessage,
    clearMessages
  } = useEnhancedWebSocket({
    url: wsUrl,
    autoConnect: false,
    autoReconnect,
    debug: false, // Disable debug logging for better performance
    updateRedux: true,
    heartbeatInterval: 60000, // Increase heartbeat interval to reduce network traffic
    reconnectInterval: 3000, // Increase reconnect interval
    maxReconnectAttempts: 3, // Reduce max reconnect attempts
    connectionTimeout: 3000 // Reduce connection timeout
  });

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle connect button click
  const handleConnect = () => {
    connect();
    message.info('Connecting to WebSocket...');
  };

  // Handle disconnect button click
  const handleDisconnect = () => {
    disconnect();
    message.info('Disconnected from WebSocket');
  };

  // Handle send message button click
  const handleSendMessage = () => {
    if (!messageText.trim() || !connected) return;

    try {
      // Try to parse as JSON if it looks like JSON
      let messageData;
      if (messageText.trim().startsWith('{') || messageText.trim().startsWith('[')) {
        messageData = JSON.parse(messageText);
      } else {
        messageData = { message: messageText };
      }

      // Send the message
      sendMessage(messageData);

      // Clear the input
      setMessageText('');
    } catch (error) {
      message.error(`Error sending message: ${error.message}`);
    }
  };

  // Handle key down in message input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!showTimestamp) return null;

    try {
      // Parse the timestamp string to a Date object
      const date = new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return null;
      }

      // Format the date with hours, minutes, seconds, and milliseconds
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return null;
    }
  };

  // Check if a string is JSON
  const isJsonString = (str) => {
    try {
      const json = JSON.parse(str);
      return typeof json === 'object';
    } catch (e) {
      return false;
    }
  };

  // Render message content
  const renderMessageContent = (content) => {
    if (typeof content === 'string' && isJsonString(content)) {
      return (
        <>
          <div className="message-content">JSON Message</div>
          <pre>{JSON.stringify(JSON.parse(content), null, 2)}</pre>
        </>
      );
    } else if (typeof content === 'object') {
      return (
        <>
          <div className="message-content">Object Message</div>
          <pre>{JSON.stringify(content, null, 2)}</pre>
        </>
      );
    } else {
      return <div className="message-content">{content}</div>;
    }
  };

  // Message templates
  const messageTemplates = [
    { name: 'Ping', content: '{"type": "ping"}' },
    { name: 'Request Data', content: '{"type": "request_data"}' },
    { name: 'Hello', content: '{"type": "message", "content": "Hello, WebSocket!"}' },
    { name: 'Status', content: '{"type": "status_request"}' }
  ];

  return (
    <WebSocketManagerContainer>
      <TabContainer role="tablist" aria-label="WebSocket Manager tabs">
        <Tab
          role="tab"
          id="tab-messages"
          aria-selected={activeTab === 'messages'}
          aria-controls="panel-messages"
          active={activeTab === 'messages'}
          onClick={() => setActiveTab('messages')}
        >
          Messages
        </Tab>
        <Tab
          role="tab"
          id="tab-settings"
          aria-selected={activeTab === 'settings'}
          aria-controls="panel-settings"
          active={activeTab === 'settings'}
          onClick={() => setActiveTab('settings')}
        >
          Settings
        </Tab>
        <Tab
          role="tab"
          id="tab-info"
          aria-selected={activeTab === 'info'}
          aria-controls="panel-info"
          active={activeTab === 'info'}
          onClick={() => setActiveTab('info')}
        >
          Info
        </Tab>
      </TabContainer>

      <div
        role="tabpanel"
        id="panel-messages"
        aria-labelledby="tab-messages"
        hidden={activeTab !== 'messages'}
      >
        {activeTab === 'messages' && (
          <>
            <AccessibleCard
              title="WebSocket Connection"
              headerActions={
                connected ? (
                  <AccessibleButton
                    variant="outline"
                    size="small"
                    onClick={handleDisconnect}
                    startIcon={<DisconnectOutlined />}
                    style={{ color: theme.colors.error.main, borderColor: theme.colors.error.main }}
                  >
                    Disconnect
                  </AccessibleButton>
                ) : (
                  <AccessibleButton
                    variant="primary"
                    size="small"
                    onClick={handleConnect}
                    startIcon={<ReloadOutlined />}
                  >
                    Connect
                  </AccessibleButton>
                )
              }
            >
              <ConnectionStatus connected={connected}>
                {connected ? (
                  <>
                    <CheckCircleOutlined aria-hidden="true" />
                    <span>Connected to {wsUrl}</span>
                  </>
                ) : (
                  <>
                    <CloseCircleOutlined aria-hidden="true" />
                    <span>{connecting ? `Connecting (attempt ${reconnectAttempt})...` : 'Disconnected'}</span>
                  </>
                )}
              </ConnectionStatus>

              {error && (
                <div style={{ marginTop: theme.spacing[2], color: theme.colors.error.main }}>
                  Error: {error.message || 'Unknown error'}
                </div>
              )}
            </AccessibleCard>

            <AccessibleCard
              title="Messages"
              headerActions={
                <AccessibleButton
                  variant="text"
                  size="small"
                  onClick={clearMessages}
                  startIcon={<ClearOutlined />}
                  ariaLabel="Clear all messages"
                >
                  Clear
                </AccessibleButton>
              }
            >
              <MessageTemplates>
                <div style={{ marginRight: theme.spacing[2], display: 'flex', alignItems: 'center' }}>
                  Templates:
                </div>
                {messageTemplates.map((template, index) => (
                  <AccessibleButton
                    key={index}
                    variant="outline"
                    size="small"
                    onClick={() => setMessageText(template.content)}
                  >
                    {template.name}
                  </AccessibleButton>
                ))}
              </MessageTemplates>

              <MessageInput>
                <AccessibleInput
                  id="message-input"
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={connected ? "Type a message..." : "Connect to send messages..."}
                  disabled={!connected}
                  fullWidth
                  as="textarea"
                  rows={3}
                  aria-label="Message to send"
                />
                <AccessibleButton
                  variant="primary"
                  onClick={handleSendMessage}
                  disabled={!connected || !messageText.trim()}
                  style={{ alignSelf: 'flex-end' }}
                  ariaLabel="Send message"
                >
                  <SendOutlined />
                </AccessibleButton>
              </MessageInput>

              <div style={{ margin: `${theme.spacing[3]} 0` }}>
                <MessageList
                  role="log"
                  aria-label="WebSocket messages"
                  aria-live="polite"
                >
                  {messages.length === 0 ? (
                    <div style={{
                      padding: theme.spacing[4],
                      textAlign: 'center',
                      color: theme.colors.neutral[500]
                    }}>
                      No messages yet
                    </div>
                  ) : (
                    messages.map((msg, index) => (
                      <Message
                        key={index}
                        type={msg.type}
                        role="article"
                        aria-label={`${msg.type === 'sent' ? 'Sent' : 'Received'} message`}
                      >
                        <div className="message-header">
                          <span>{msg.type === 'sent' ? 'Sent' : 'Received'}</span>
                          <span className="timestamp">{formatTimestamp(msg.timestamp) || 'Unknown time'}</span>
                        </div>
                        {renderMessageContent(msg.content)}
                      </Message>
                    ))
                  )}
                  <div ref={messagesEndRef} tabIndex={-1} />
                </MessageList>
              </div>
            </AccessibleCard>
          </>
        )}
      </div>

      <div
        role="tabpanel"
        id="panel-settings"
        aria-labelledby="tab-settings"
        hidden={activeTab !== 'settings'}
      >
        {activeTab === 'settings' && (
          <AccessibleCard
            title="WebSocket Settings"
            headerIcon={<SettingOutlined />}
          >
            <SettingsPanel>
              <SettingsGroup>
                <AccessibleInput
                  id="ws-url"
                  label="WebSocket URL"
                  value={wsUrl}
                  onChange={(e) => setWsUrl(e.target.value)}
                  placeholder="ws://localhost:8000/ws/"
                  fullWidth
                />
              </SettingsGroup>

              <SettingsGroup>
                <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
                  <input
                    type="checkbox"
                    id="auto-reconnect"
                    checked={autoReconnect}
                    onChange={(e) => setAutoReconnect(e.target.checked)}
                  />
                  <label htmlFor="auto-reconnect">Auto-reconnect on disconnect</label>
                </div>
              </SettingsGroup>

              <SettingsGroup>
                <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
                  <input
                    type="checkbox"
                    id="show-timestamp"
                    checked={showTimestamp}
                    onChange={(e) => setShowTimestamp(e.target.checked)}
                  />
                  <label htmlFor="show-timestamp">Show message timestamps</label>
                </div>
              </SettingsGroup>

              <div style={{ marginTop: theme.spacing[2] }}>
                <AccessibleButton
                  variant="primary"
                  onClick={handleConnect}
                  startIcon={<ReloadOutlined />}
                >
                  Apply Settings & Connect
                </AccessibleButton>
              </div>
            </SettingsPanel>
          </AccessibleCard>
        )}
      </div>

      <div
        role="tabpanel"
        id="panel-info"
        aria-labelledby="tab-info"
        hidden={activeTab !== 'info'}
      >
        {activeTab === 'info' && (
          <AccessibleCard
            title="WebSocket Information"
            headerIcon={<InfoCircleOutlined />}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing[4] }}>
              <div>
                <h3>About WebSockets</h3>
                <p>
                  WebSocket is a communication protocol that provides full-duplex communication channels over a single TCP connection.
                  It enables real-time, bidirectional communication between a client and server.
                </p>
              </div>

              <div>
                <h3>Usage Examples</h3>
                <div style={{
                  backgroundColor: theme.colors.neutral[100],
                  padding: theme.spacing[3],
                  borderRadius: theme.borderRadius.md,
                  fontFamily: theme.typography.fontFamily.code
                }}>
                  <div style={{ marginBottom: theme.spacing[2] }}>
                    <strong>Ping Message:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"ping\"\n}"}</pre>
                  </div>

                  <div style={{ marginBottom: theme.spacing[2] }}>
                    <strong>Request Data:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"request_data\"\n}"}</pre>
                  </div>

                  <div>
                    <strong>Send Message:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"message\",\n  \"content\": \"Hello, WebSocket!\"\n}"}</pre>
                  </div>
                </div>
              </div>

              <div>
                <h3>Troubleshooting</h3>
                <ul style={{ paddingLeft: theme.spacing[4] }}>
                  <li>Make sure the WebSocket server is running</li>
                  <li>Check that the WebSocket URL is correct</li>
                  <li>Ensure there are no network restrictions blocking WebSocket connections</li>
                  <li>Check browser console for any connection errors</li>
                </ul>
              </div>
            </div>
          </AccessibleCard>
        )}
      </div>
    </WebSocketManagerContainer>
  );
};

export default ImprovedWebSocketManager;
