import { configureStore } from '@reduxjs/toolkit';
import rootReducer from './reducers';
import websocketMiddleware from './middleware/websocketMiddleware';

/**
 * Performance monitoring middleware
 * Tracks the execution time of Redux actions and logs performance metrics
 */
const performanceMiddleware = store => next => action => {
  // Record the start time
  const start = performance.now();

  // Log the action type and start time (in development only)
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Redux] Action ${action.type} started at ${new Date().toISOString()}`);
  }

  // Call the next middleware in the chain
  const result = next(action);

  // Calculate the duration
  const duration = performance.now() - start;

  // Log the action type and duration (in development only)
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Redux] Action ${action.type} took ${duration.toFixed(2)}ms to complete`);
  }

  // Emit a custom event for the performance monitor
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('redux-action', {
      detail: {
        type: action.type,
        duration,
        timestamp: new Date().toISOString(),
        state: store.getState() // Include current state for debugging
      }
    }));
  }

  return result;
};

/**
 * Configure the Redux store
 * Uses Redux Toolkit's configureStore for better defaults and dev tools integration
 */
const createAppStore = (preloadedState = {}) => {
  // Ensure websocket state is always available
  const defaultState = {
    websocket: {
      connected: false,
      connecting: false,
      error: null,
      lastMessage: null,
      messages: [],
      status: 'disconnected',
      url: null,
      reconnectAttempt: 0,
      closeInfo: null,
      willReconnect: false
    },
    ...preloadedState
  };

  return configureStore({
    reducer: rootReducer,
    preloadedState: defaultState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types in serializability checks
          ignoredActions: [
            'WS_MESSAGE_RECEIVED',
            'WS_ERROR',
            'WEBSOCKET_MESSAGE_RECEIVED',
            'WEBSOCKET_ERROR'
          ],
          // Ignore these field paths in serializability checks
          ignoredPaths: [
            'websocket.socket',
            'error.originalError',
            'webSocketClient.socket'
          ]
        },
        thunk: true
      }).concat(websocketMiddleware(), performanceMiddleware),
    // Enable Redux DevTools integration
    devTools: {
      name: 'App Builder 201',
      trace: true,
      traceLimit: 25
    }
  });
};

// Create the store
const store = createAppStore();

// Export the store and the store creator function
export default store;

